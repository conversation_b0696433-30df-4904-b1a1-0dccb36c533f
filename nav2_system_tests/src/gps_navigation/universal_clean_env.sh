#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 通用版环境清理脚本
# 基于ROS2 API动态发现和清理运行中的节点和进程

echo "=========================================="
echo "通用版ROS环境清理脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取当前脚本的PID，避免杀死自己
SCRIPT_PID=$$
log_info "当前脚本PID: $SCRIPT_PID"

# 1. 保存当前环境变量
log_info "保存当前ROS环境变量..."
SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION

# 2. 基于ROS2 API的智能清理
log_info "基于ROS2 API动态发现运行中的节点..."

# 获取当前运行的所有ROS节点
RUNNING_NODES=""
if command -v ros2 &> /dev/null; then
    RUNNING_NODES=$(ros2 node list 2>/dev/null || echo "")
    if [ -n "$RUNNING_NODES" ]; then
        log_info "发现运行中的ROS节点:"
        echo "$RUNNING_NODES" | while read -r node; do
            if [ -n "$node" ]; then
                echo "  - $node"
            fi
        done
    else
        log_info "未发现运行中的ROS节点"
    fi
fi

# 获取当前活跃的话题
ACTIVE_TOPICS=""
if command -v ros2 &> /dev/null; then
    ACTIVE_TOPICS=$(ros2 topic list 2>/dev/null || echo "")
    if [ -n "$ACTIVE_TOPICS" ]; then
        log_info "发现活跃话题:"
        echo "$ACTIVE_TOPICS" | while read -r topic; do
            if [ -n "$topic" ] && [ "$topic" != "/parameter_events" ] && [ "$topic" != "/rosout" ]; then
                echo "  - $topic"
            fi
        done
    fi
fi

# 3. 优雅地停止ROS2守护进程
log_info "停止ROS2守护进程..."
ros2 daemon stop 2>/dev/null || true
sleep 1

# 4. 基于进程树的智能清理
log_info "基于进程树进行智能清理..."

# 定义ROS相关进程模式
ROS_PATTERNS=(
    "ros2.*launch"
    "ros2.*run"
    "ros_gz_bridge"
    "parameter_bridge"
    "gazebo"
    "gz.*sim"
    "gzserver"
    "gzclient"
    "rviz2"
    "rqt"
    "foxglove_bridge"
    "nav2_.*"
    "controller_server"
    "planner_server"
    "behavior_server"
    "bt_navigator"
    "lifecycle_manager"
    "robot_state_publisher"
    "static_transform_publisher"
    "component_container"
    "map_server"
    "amcl"
    "ekf_node"
    "navsat_transform_node"
    "robot_localization"
    "turtlebot3"
)

# 安全地清理匹配的进程
for pattern in "${ROS_PATTERNS[@]}"; do
    PIDS=$(pgrep -f "$pattern" 2>/dev/null | grep -v "^$SCRIPT_PID$" || true)
    if [ -n "$PIDS" ]; then
        log_info "清理进程模式: $pattern"
        echo "$PIDS" | while read -r pid; do
            if [ -n "$pid" ] && [ "$pid" != "$SCRIPT_PID" ]; then
                PROCESS_NAME=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
                log_info "  终止进程: $pid ($PROCESS_NAME)"
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done
    fi
done

# 等待进程优雅退出
sleep 3

# 强制清理仍在运行的进程
log_info "强制清理剩余进程..."
for pattern in "${ROS_PATTERNS[@]}"; do
    PIDS=$(pgrep -f "$pattern" 2>/dev/null | grep -v "^$SCRIPT_PID$" || true)
    if [ -n "$PIDS" ]; then
        echo "$PIDS" | while read -r pid; do
            if [ -n "$pid" ] && [ "$pid" != "$SCRIPT_PID" ]; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
    fi
done

# 5. 清理缓存和临时文件
log_info "清理ROS2缓存和临时文件..."
rm -rf ~/.ros/log/* 2>/dev/null || true
rm -rf /tmp/.ros* 2>/dev/null || true
find /tmp -name "nav2_*" -type f -delete 2>/dev/null || true
find /tmp -name "costmap_*" -type f -delete 2>/dev/null || true
find /tmp -name "amcl_*" -type f -delete 2>/dev/null || true
find /tmp -name "tf_*" -type f -delete 2>/dev/null || true
find /tmp -name "gazebo*" -type f -delete 2>/dev/null || true
find /tmp -name "gz*" -type f -delete 2>/dev/null || true

# 清理Gazebo缓存
rm -rf ~/.gazebo/log/* 2>/dev/null || true
rm -rf ~/.gz/log/* 2>/dev/null || true

# 6. 重置环境变量
log_info "重置ROS环境变量..."
unset ROS_DOMAIN_ID
unset RMW_IMPLEMENTATION
unset CYCLONEDX_URI
unset FASTRTPS_DEFAULT_PROFILES_FILE

# 7. 重启ROS2守护进程
log_info "重启ROS2守护进程..."
ros2 daemon start 2>/dev/null || true
sleep 2

# 8. 恢复环境变量
log_info "恢复ROS环境变量..."
if [ -n "$SAVED_ROS_DOMAIN_ID" ]; then
    export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
    log_success "恢复 ROS_DOMAIN_ID=$ROS_DOMAIN_ID"
else
    export ROS_DOMAIN_ID=11
    log_info "设置默认 ROS_DOMAIN_ID=11"
fi

if [ -n "$SAVED_RMW_IMPLEMENTATION" ]; then
    export RMW_IMPLEMENTATION=$SAVED_RMW_IMPLEMENTATION
    log_success "恢复 RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION"
else
    export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
    log_info "设置推荐的 RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
fi

# 设置推荐的DDS配置
export CYCLONEDX_URI='<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'

# 9. 验证清理结果
log_info "设置工作空间环境..."
source /opt/overlay_ws/install/setup.bash 2>/dev/null || true

log_info "验证清理结果..."
REMAINING_TOPICS=$(ros2 topic list 2>/dev/null || echo "无法获取话题列表")
REMAINING_NODES=$(ros2 node list 2>/dev/null || echo "无法获取节点列表")

echo "剩余话题:"
echo "$REMAINING_TOPICS"
echo ""

echo "剩余节点:"
echo "$REMAINING_NODES"
echo ""

# 检查清理是否成功
UNWANTED_TOPICS=$(echo "$REMAINING_TOPICS" | grep -v -E "^(/parameter_events|/rosout)$" || true)
if [ -z "$UNWANTED_TOPICS" ]; then
    log_success "环境清理成功！只剩下系统基础话题。"
else
    log_warn "仍有一些话题未清理:"
    echo "$UNWANTED_TOPICS"
fi

# 10. 同步文件系统
log_info "同步文件系统..."
sync
log_success "通用清理脚本执行完成！"
