#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 通用环境清理脚本包装器
# 提供简单的命令行接口来使用不同的清理策略

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_CLEANER="$SCRIPT_DIR/smart_env_cleaner.py"
BASH_CLEANER="$SCRIPT_DIR/universal_clean_env.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "Navigation2 环境清理工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -l, --list              列出所有可用的清理策略"
    echo "  -s, --strategy STRATEGY 指定清理策略 (light|standard|full|simulation_only)"
    echo "  -d, --dry-run           只显示将要清理的内容，不实际执行"
    echo "  -f, --force             使用传统bash清理器（更激进）"
    echo "  -q, --quick             快速清理（等同于 --strategy light）"
    echo ""
    echo "清理策略说明:"
    echo "  light         - 轻量级清理，只清理Nav2核心组件和测试进程"
    echo "  standard      - 标准清理，清理大部分ROS进程（默认）"
    echo "  full          - 完全清理，清理所有ROS相关进程"
    echo "  simulation_only - 只清理仿真相关进程"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用标准策略清理"
    echo "  $0 -s light            # 使用轻量级策略清理"
    echo "  $0 -d -s full          # 干运行模式查看完全清理将清理什么"
    echo "  $0 -f                  # 使用传统bash清理器"
    echo "  $0 -q                  # 快速清理"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查依赖
check_dependencies() {
    # 检查Python清理器是否存在
    if [ ! -f "$PYTHON_CLEANER" ]; then
        log_error "Python清理器不存在: $PYTHON_CLEANER"
        return 1
    fi
    
    # 检查bash清理器是否存在
    if [ ! -f "$BASH_CLEANER" ]; then
        log_error "Bash清理器不存在: $BASH_CLEANER"
        return 1
    fi
    
    # 检查Python依赖
    if ! python3 -c "import yaml, psutil, rclpy" 2>/dev/null; then
        log_warn "Python依赖不完整，将使用bash清理器"
        return 1
    fi
    
    return 0
}

# 解析命令行参数
STRATEGY="standard"
DRY_RUN=false
USE_BASH=false
SHOW_HELP=false
LIST_STRATEGIES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            SHOW_HELP=true
            shift
            ;;
        -l|--list)
            LIST_STRATEGIES=true
            shift
            ;;
        -s|--strategy)
            STRATEGY="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            USE_BASH=true
            shift
            ;;
        -q|--quick)
            STRATEGY="light"
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 显示帮助
if [ "$SHOW_HELP" = true ]; then
    show_help
    exit 0
fi

# 列出策略
if [ "$LIST_STRATEGIES" = true ]; then
    if check_dependencies; then
        python3 "$PYTHON_CLEANER" --list-strategies
    else
        log_error "无法列出策略，Python依赖不完整"
        exit 1
    fi
    exit 0
fi

# 主清理逻辑
log_info "开始环境清理..."

if [ "$USE_BASH" = true ]; then
    # 使用bash清理器
    log_info "使用传统bash清理器"
    "$BASH_CLEANER"
    exit $?
fi

# 检查依赖并决定使用哪个清理器
if check_dependencies; then
    # 使用Python清理器
    log_info "使用智能Python清理器，策略: $STRATEGY"
    
    PYTHON_ARGS="--strategy $STRATEGY"
    if [ "$DRY_RUN" = true ]; then
        PYTHON_ARGS="$PYTHON_ARGS --dry-run"
    fi
    
    python3 "$PYTHON_CLEANER" $PYTHON_ARGS
    exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "环境清理完成"
    else
        log_error "环境清理失败"
    fi
    
    exit $exit_code
else
    # 回退到bash清理器
    log_warn "Python依赖不完整，回退到bash清理器"
    "$BASH_CLEANER"
    exit $?
fi
