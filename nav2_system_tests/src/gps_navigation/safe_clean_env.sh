#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 安全版环境清理脚本
# 专门用于解决GPS导航测试中的时间跳跃和缓存问题

echo "=========================================="
echo "安全版ROS环境清理脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 获取当前脚本的PID，避免杀死自己
SCRIPT_PID=$$
log_info "当前脚本PID: $SCRIPT_PID"

# 1. 保存当前环境变量
log_info "保存当前ROS环境变量..."
SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION

# 2. 安全地停止ROS相关进程
log_info "安全地停止ROS相关进程..."

# 首先尝试优雅地停止ROS2守护进程
log_info "停止ROS2守护进程..."
ros2 daemon stop 2>/dev/null || true
sleep 1

# 安全地清理特定进程（避免杀死脚本自身和重要系统进程）
log_info "清理特定ROS进程..."

# 1. 清理Gazebo/仿真相关进程
log_info "清理Gazebo仿真进程..."
pkill -f "gz sim" 2>/dev/null || true
pkill -f "gazebo" 2>/dev/null || true
pkill -f "gzserver" 2>/dev/null || true
pkill -f "gzclient" 2>/dev/null || true

# 2. 清理可视化工具
log_info "清理可视化工具..."
pkill -f "rviz2" 2>/dev/null || true
pkill -f "rqt" 2>/dev/null || true
pkill -f "foxglove_bridge" 2>/dev/null || true

# 3. 清理Nav2核心导航组件（按lifecycle_nodes顺序）
log_info "清理Nav2导航组件..."
pkill -f "controller_server" 2>/dev/null || true
pkill -f "smoother_server" 2>/dev/null || true
pkill -f "planner_server" 2>/dev/null || true
pkill -f "behavior_server" 2>/dev/null || true
pkill -f "velocity_smoother" 2>/dev/null || true
pkill -f "collision_monitor" 2>/dev/null || true
pkill -f "bt_navigator" 2>/dev/null || true
pkill -f "waypoint_follower" 2>/dev/null || true

# 4. 清理定位相关组件
log_info "清理定位组件..."
pkill -f "amcl" 2>/dev/null || true
pkill -f "map_server" 2>/dev/null || true

# 5. 清理robot_localization相关进程
log_info "清理robot_localization组件..."
pkill -f "ekf_node" 2>/dev/null || true
pkill -f "navsat_transform_node" 2>/dev/null || true
pkill -f "robot_localization" 2>/dev/null || true

# 6. 清理GPS导航测试的Python包装器和监控节点
log_info "清理GPS导航测试组件..."
pkill -f "robot_localization_lifecycle_wrapper.py" 2>/dev/null || true
pkill -f "indoor_ekf_lifecycle_wrapper.py" 2>/dev/null || true
pkill -f "sensor_monitor.py" 2>/dev/null || true
pkill -f "tf_monitor.py" 2>/dev/null || true
pkill -f "nav_monitor.py" 2>/dev/null || true
pkill -f "environment_detector_node.py" 2>/dev/null || true
pkill -f "map_manager_node.py" 2>/dev/null || true
pkill -f "amcl_auto_initializer.py" 2>/dev/null || true

# 7. 清理生命周期管理器
log_info "清理生命周期管理器..."
pkill -f "lifecycle_manager" 2>/dev/null || true

# 8. 清理基础ROS组件
log_info "清理基础ROS组件..."
pkill -f "robot_state_publisher" 2>/dev/null || true
pkill -f "static_transform_publisher" 2>/dev/null || true

# 9. 清理组件容器
log_info "清理组件容器..."
pkill -f "component_container" 2>/dev/null || true
pkill -f "component_container_isolated" 2>/dev/null || true
pkill -f "component_container_mt" 2>/dev/null || true

# 10. 清理Nav2启动相关进程
log_info "清理Nav2启动进程..."
pkill -f "nav2_bringup" 2>/dev/null || true
pkill -f "navigation_launch.py" 2>/dev/null || true
pkill -f "bringup_launch.py" 2>/dev/null || true

# 11. 清理TurtleBot3相关进程
log_info "清理TurtleBot3相关进程..."
pkill -f "turtlebot3" 2>/dev/null || true
pkill -f "spawn_tb3_gps.launch.py" 2>/dev/null || true

sleep 2

# 3. 清理缓存文件
log_info "清理ROS2缓存..."
rm -rf ~/.ros/log/* 2>/dev/null || true
rm -rf /tmp/.ros* 2>/dev/null || true

# 清理特定的临时文件
log_info "清理导航相关缓存..."
find /tmp -name "nav2_*" -type f -delete 2>/dev/null || true
find /tmp -name "costmap_*" -type f -delete 2>/dev/null || true
find /tmp -name "amcl_*" -type f -delete 2>/dev/null || true
find /tmp -name "tf_*" -type f -delete 2>/dev/null || true

# 清理Gazebo缓存
log_info "清理Gazebo缓存..."
rm -rf ~/.gazebo/log/* 2>/dev/null || true
rm -rf ~/.gz/log/* 2>/dev/null || true
find /tmp -name "gazebo*" -type f -delete 2>/dev/null || true
find /tmp -name "gz*" -type f -delete 2>/dev/null || true

# 4. 重置环境变量
log_info "重置ROS环境变量..."
unset ROS_DOMAIN_ID
unset RMW_IMPLEMENTATION
unset CYCLONEDDS_URI
unset FASTRTPS_DEFAULT_PROFILES_FILE

# 5. 重启ROS2守护进程
log_info "重启ROS2守护进程..."
ros2 daemon start 2>/dev/null || true
sleep 2

# 6. 恢复环境变量
log_info "恢复ROS环境变量..."
if [ -n "$SAVED_ROS_DOMAIN_ID" ]; then
    export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
    log_success "恢复 ROS_DOMAIN_ID=$ROS_DOMAIN_ID"
else
    export ROS_DOMAIN_ID=11
    log_info "设置默认 ROS_DOMAIN_ID=11"
fi

if [ -n "$SAVED_RMW_IMPLEMENTATION" ]; then
    export RMW_IMPLEMENTATION=$SAVED_RMW_IMPLEMENTATION
    log_success "恢复 RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION"
else
    export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
    log_info "设置推荐的 RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
fi

# 设置推荐的DDS配置
export CYCLONEDDS_URI='<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'

# 7. 验证环境（按原版脚本顺序）
log_info "设置工作空间环境..."
source /opt/overlay_ws/install/setup.bash 2>/dev/null || true

log_info "检查话题列表..."
TOPICS=$(ros2 topic list 2>/dev/null || echo "无法获取话题列表")
echo "$TOPICS"
echo ""

# 8. 同步文件系统
log_info "同步文件系统..."
sync
log_success "脚本执行完成！现在可以安全启动导航系统。"
