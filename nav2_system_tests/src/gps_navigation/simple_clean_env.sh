#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 简单而强大的ROS环境完全清理脚本
# 专注于完全清理，无复杂配置，一个文件搞定

echo "=========================================="
echo "ROS环境完全清理脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }

# 获取当前脚本PID，避免杀死自己
SCRIPT_PID=$$
log_info "当前脚本PID: $SCRIPT_PID"

# 保存环境变量
log_info "保存当前ROS环境变量..."
SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION

# 停止ROS2守护进程
log_info "停止ROS2守护进程..."
ros2 daemon stop 2>/dev/null || true
sleep 1

# 完全清理所有ROS相关进程
log_info "完全清理所有ROS相关进程..."

# 定义所有需要清理的进程模式（一次性列出所有）
PROCESS_PATTERNS=(
    "ros2.*launch"
    "ros2.*run"
    "event_driven_launch"
    "nav2_system_tests"
    "navigation_launch"
    "bringup_launch"
    "nav2_bringup"
    "gazebo"
    "gz.*sim"
    "gzserver"
    "gzclient"
    "ros_gz_bridge"
    "parameter_bridge"
    "spawn_tb3"
    "turtlebot3"
    "rviz2"
    "rqt"
    "foxglove_bridge"
    "controller_server"
    "smoother_server"
    "planner_server"
    "behavior_server"
    "velocity_smoother"
    "collision_monitor"
    "bt_navigator"
    "waypoint_follower"
    "amcl"
    "map_server"
    "ekf_node"
    "navsat_transform_node"
    "robot_localization"
    "robot_state_publisher"
    "static_transform_publisher"
    "lifecycle_manager"
    "component_container"
    "robot_localization_lifecycle_wrapper"
    "indoor_ekf_lifecycle_wrapper"
    "sensor_monitor"
    "tf_monitor"
    "nav_monitor"
    "environment_detector_node"
    "map_manager_node"
    "amcl_auto_initializer"
)

# 第一轮：优雅终止（SIGTERM）
log_info "第一轮：优雅终止进程..."
for pattern in "${PROCESS_PATTERNS[@]}"; do
    # 查找匹配的进程，排除当前脚本
    PIDS=$(pgrep -f "$pattern" 2>/dev/null | grep -v "^$SCRIPT_PID$" || true)
    if [ -n "$PIDS" ]; then
        echo "$PIDS" | while read -r pid; do
            if [ -n "$pid" ] && [ "$pid" != "$SCRIPT_PID" ]; then
                PROCESS_NAME=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
                log_info "  优雅终止: $pid ($PROCESS_NAME)"
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done
    fi
done

# 等待进程优雅退出
log_info "等待进程优雅退出..."
sleep 3

# 第二轮：强制终止（SIGKILL）
log_info "第二轮：强制终止剩余进程..."
for pattern in "${PROCESS_PATTERNS[@]}"; do
    PIDS=$(pgrep -f "$pattern" 2>/dev/null | grep -v "^$SCRIPT_PID$" || true)
    if [ -n "$PIDS" ]; then
        echo "$PIDS" | while read -r pid; do
            if [ -n "$pid" ] && [ "$pid" != "$SCRIPT_PID" ]; then
                PROCESS_NAME=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
                log_info "  强制终止: $pid ($PROCESS_NAME)"
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
    fi
done

sleep 1

# 清理所有缓存和临时文件
log_info "清理缓存和临时文件..."
rm -rf ~/.ros/log/* 2>/dev/null || true
rm -rf /tmp/.ros* 2>/dev/null || true
rm -rf ~/.gazebo/log/* 2>/dev/null || true
rm -rf ~/.gz/log/* 2>/dev/null || true

# 清理特定的临时文件
find /tmp -name "nav2_*" -type f -delete 2>/dev/null || true
find /tmp -name "costmap_*" -type f -delete 2>/dev/null || true
find /tmp -name "amcl_*" -type f -delete 2>/dev/null || true
find /tmp -name "tf_*" -type f -delete 2>/dev/null || true
find /tmp -name "gazebo*" -type f -delete 2>/dev/null || true
find /tmp -name "gz*" -type f -delete 2>/dev/null || true

# 重置环境变量
log_info "重置ROS环境变量..."
unset ROS_DOMAIN_ID
unset RMW_IMPLEMENTATION
unset CYCLONEDDS_URI
unset FASTRTPS_DEFAULT_PROFILES_FILE

# 重启ROS2守护进程
log_info "重启ROS2守护进程..."
ros2 daemon start 2>/dev/null || true
sleep 2

# 恢复环境变量
log_info "恢复ROS环境变量..."
if [ -n "$SAVED_ROS_DOMAIN_ID" ]; then
    export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
    log_success "恢复 ROS_DOMAIN_ID=$ROS_DOMAIN_ID"
else
    export ROS_DOMAIN_ID=11
    log_info "设置默认 ROS_DOMAIN_ID=11"
fi

if [ -n "$SAVED_RMW_IMPLEMENTATION" ]; then
    export RMW_IMPLEMENTATION=$SAVED_RMW_IMPLEMENTATION
    log_success "恢复 RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION"
else
    export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
    log_info "设置推荐的 RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
fi

# 设置推荐的DDS配置
export CYCLONEDDS_URI='<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'

# 验证清理结果
log_info "设置工作空间环境..."
source /opt/overlay_ws/install/setup.bash 2>/dev/null || true

log_info "验证清理结果..."
REMAINING_TOPICS=$(ros2 topic list 2>/dev/null || echo "无法获取话题列表")
echo "剩余话题:"
echo "$REMAINING_TOPICS"
echo ""

# 检查清理效果
UNWANTED_TOPICS=$(echo "$REMAINING_TOPICS" | grep -v -E "^(/parameter_events|/rosout)$" || true)
if [ -z "$UNWANTED_TOPICS" ]; then
    log_success "✅ 环境完全清理成功！只剩下系统基础话题。"
else
    log_warn "⚠️  仍有一些话题未清理:"
    echo "$UNWANTED_TOPICS"
fi

# 同步文件系统
log_info "同步文件系统..."
sync
log_success "完全清理脚本执行完成！"
