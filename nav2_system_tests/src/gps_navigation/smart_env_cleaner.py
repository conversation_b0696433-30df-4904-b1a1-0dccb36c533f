#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
智能环境清理器

基于ROS2 API和系统进程信息的智能环境清理工具。
能够动态发现运行中的ROS节点、话题和相关进程，并进行精确清理。
支持配置文件驱动的清理策略。
"""

import os
import sys
import time
import signal
import psutil
import subprocess
import yaml
import argparse
from typing import List, Set, Dict, Optional
import rclpy
from rclpy.node import Node


class SmartEnvCleaner(Node):
    """智能环境清理器"""

    def __init__(self, config_file: str = None, strategy: str = 'standard'):
        super().__init__('smart_env_cleaner')

        # 加载配置文件
        self.config = self.load_config(config_file)
        self.strategy = strategy

        # 从配置文件获取系统基础话题和节点
        self.system_topics = set(self.config.get('system_topics', ['/parameter_events', '/rosout']))
        self.system_nodes = set(self.config.get('system_nodes', ['/ros2cli_daemon']))

        # 获取当前策略的配置
        self.strategy_config = self.config.get('cleanup_strategies', {}).get(strategy, {})

        self.get_logger().info(f"智能环境清理器已初始化，使用策略: {strategy}")

    def load_config(self, config_file: str = None) -> Dict:
        """加载配置文件"""
        if config_file is None:
            # 默认配置文件路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_file = os.path.join(script_dir, 'cleanup_config.yaml')

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                self.get_logger().info(f"已加载配置文件: {config_file}")
                return config
        except Exception as e:
            self.get_logger().warning(f"无法加载配置文件 {config_file}: {e}")
            # 返回默认配置
            return self.get_default_config()

    def get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'system_topics': ['/parameter_events', '/rosout'],
            'system_nodes': ['/ros2cli_daemon'],
            'process_patterns': {
                'all_processes': [
                    'ros2.*launch', 'ros2.*run', 'gazebo', 'gz.*sim', 'rviz2',
                    'nav2_', 'controller_server', 'planner_server', 'behavior_server',
                    'bt_navigator', 'lifecycle_manager', 'robot_state_publisher',
                    'static_transform_publisher', 'component_container', 'map_server',
                    'amcl', 'ekf_node', 'navsat_transform_node', 'robot_localization',
                    'turtlebot3', 'parameter_bridge', 'ros_gz_bridge'
                ]
            },
            'cleanup_strategies': {
                'standard': {
                    'include_patterns': ['all_processes'],
                    'clear_cache': True,
                    'restart_daemon': True
                }
            },
            'timeouts': {
                'graceful_shutdown': 3,
                'force_kill_wait': 1,
                'daemon_restart': 2,
                'ros_api_timeout': 10
            }
        }

    def discover_ros_nodes(self) -> List[str]:
        """发现当前运行的ROS节点"""
        try:
            result = subprocess.run(['ros2', 'node', 'list'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                nodes = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                self.get_logger().info(f"发现 {len(nodes)} 个ROS节点")
                return nodes
            else:
                self.get_logger().warning("无法获取ROS节点列表")
                return []
        except Exception as e:
            self.get_logger().error(f"获取ROS节点列表失败: {e}")
            return []

    def discover_active_topics(self) -> List[str]:
        """发现当前活跃的话题"""
        try:
            result = subprocess.run(['ros2', 'topic', 'list'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                topics = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                # 过滤掉系统基础话题
                user_topics = [t for t in topics if t not in self.system_topics]
                self.get_logger().info(f"发现 {len(user_topics)} 个用户话题")
                return user_topics
            else:
                self.get_logger().warning("无法获取话题列表")
                return []
        except Exception as e:
            self.get_logger().error(f"获取话题列表失败: {e}")
            return []

    def get_process_patterns(self) -> List[str]:
        """根据当前策略获取进程模式"""
        patterns = []
        include_patterns = self.strategy_config.get('include_patterns', [])

        for pattern_group in include_patterns:
            group_patterns = self.config.get('process_patterns', {}).get(pattern_group, [])
            patterns.extend(group_patterns)

        return patterns

    def find_ros_processes(self) -> Dict[int, str]:
        """查找所有ROS相关进程"""
        ros_processes = {}
        current_pid = os.getpid()
        patterns = self.get_process_patterns()

        if not patterns:
            self.get_logger().warning("未找到进程模式配置")
            return ros_processes

        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                pid = proc.info['pid']
                name = proc.info['name']
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 跳过当前脚本进程
                if pid == current_pid:
                    continue

                # 检查是否匹配任何模式
                full_command = f"{name} {cmdline}".lower()
                for pattern in patterns:
                    if self.match_pattern(pattern.lower(), full_command):
                        ros_processes[pid] = f"{name} ({cmdline[:100]}...)"
                        break

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        self.get_logger().info(f"发现 {len(ros_processes)} 个ROS相关进程")
        return ros_processes

    def match_pattern(self, pattern: str, text: str) -> bool:
        """匹配模式（支持简单的通配符）"""
        import re
        # 将shell风格的通配符转换为正则表达式
        regex_pattern = pattern.replace('*', '.*').replace('?', '.')
        try:
            return bool(re.search(regex_pattern, text))
        except re.error:
            # 如果正则表达式无效，使用简单的字符串包含匹配
            return pattern in text

    def stop_ros_daemon(self):
        """停止ROS2守护进程"""
        try:
            self.get_logger().info("停止ROS2守护进程...")
            subprocess.run(['ros2', 'daemon', 'stop'],
                          capture_output=True, timeout=10)
            time.sleep(1)
        except Exception as e:
            self.get_logger().warning(f"停止ROS2守护进程失败: {e}")

    def terminate_processes(self, processes: Dict[int, str], force: bool = False):
        """终止进程"""
        signal_type = signal.SIGKILL if force else signal.SIGTERM
        signal_name = "SIGKILL" if force else "SIGTERM"

        for pid, description in processes.items():
            try:
                if psutil.pid_exists(pid):
                    proc = psutil.Process(pid)
                    self.get_logger().info(f"发送 {signal_name} 到进程 {pid}: {description}")
                    proc.send_signal(signal_type)
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                self.get_logger().warning(f"无法终止进程 {pid}: {e}")

    def clean_cache_files(self):
        """清理缓存文件"""
        self.get_logger().info("清理缓存文件...")

        cache_patterns = [
            "~/.ros/log/*",
            "/tmp/.ros*",
            "/tmp/nav2_*",
            "/tmp/costmap_*",
            "/tmp/amcl_*",
            "/tmp/tf_*",
            "/tmp/gazebo*",
            "/tmp/gz*",
            "~/.gazebo/log/*",
            "~/.gz/log/*"
        ]

        for pattern in cache_patterns:
            try:
                subprocess.run(f"rm -rf {pattern}", shell=True,
                             capture_output=True, timeout=5)
            except Exception as e:
                self.get_logger().warning(f"清理 {pattern} 失败: {e}")

    def restart_ros_daemon(self):
        """重启ROS2守护进程"""
        try:
            self.get_logger().info("重启ROS2守护进程...")
            subprocess.run(['ros2', 'daemon', 'start'],
                          capture_output=True, timeout=10)
            time.sleep(2)
        except Exception as e:
            self.get_logger().warning(f"重启ROS2守护进程失败: {e}")

    def verify_cleanup(self) -> bool:
        """验证清理结果"""
        self.get_logger().info("验证清理结果...")

        # 检查剩余话题
        remaining_topics = self.discover_active_topics()
        remaining_processes = self.find_ros_processes()

        self.get_logger().info(f"剩余用户话题: {len(remaining_topics)}")
        for topic in remaining_topics:
            self.get_logger().info(f"  - {topic}")

        self.get_logger().info(f"剩余ROS进程: {len(remaining_processes)}")
        for pid, desc in remaining_processes.items():
            self.get_logger().info(f"  - {pid}: {desc}")

        # 如果只剩下系统基础话题，认为清理成功
        success = len(remaining_topics) == 0 and len(remaining_processes) == 0

        if success:
            self.get_logger().info("✅ 环境清理成功！")
        else:
            self.get_logger().warning("⚠️  仍有一些资源未完全清理")

        return success

    def run_cleanup(self):
        """执行完整的清理流程"""
        self.get_logger().info("开始智能环境清理...")

        # 1. 发现当前状态
        nodes = self.discover_ros_nodes()
        topics = self.discover_active_topics()
        processes = self.find_ros_processes()

        if not nodes and not topics and not processes:
            self.get_logger().info("环境已经是干净的，无需清理")
            return True

        # 2. 停止ROS守护进程
        self.stop_ros_daemon()

        # 3. 优雅地终止进程
        if processes:
            self.get_logger().info("优雅地终止ROS进程...")
            self.terminate_processes(processes, force=False)
            time.sleep(3)

        # 4. 强制终止剩余进程
        remaining_processes = self.find_ros_processes()
        if remaining_processes:
            self.get_logger().info("强制终止剩余进程...")
            self.terminate_processes(remaining_processes, force=True)
            time.sleep(1)

        # 5. 清理缓存文件
        self.clean_cache_files()

        # 6. 重启ROS守护进程
        self.restart_ros_daemon()

        # 7. 验证清理结果
        return self.verify_cleanup()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能ROS环境清理器')
    parser.add_argument('--config', '-c', type=str,
                       help='配置文件路径 (默认: cleanup_config.yaml)')
    parser.add_argument('--strategy', '-s', type=str, default='standard',
                       choices=['light', 'standard', 'full', 'simulation_only'],
                       help='清理策略 (默认: standard)')
    parser.add_argument('--list-strategies', action='store_true',
                       help='列出所有可用的清理策略')
    parser.add_argument('--dry-run', action='store_true',
                       help='只显示将要清理的内容，不实际执行')

    args = parser.parse_args()

    # 如果只是列出策略，不需要初始化ROS
    if args.list_strategies:
        try:
            config_file = args.config
            if config_file is None:
                script_dir = os.path.dirname(os.path.abspath(__file__))
                config_file = os.path.join(script_dir, 'cleanup_config.yaml')

            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                strategies = config.get('cleanup_strategies', {})

                print("可用的清理策略:")
                for name, strategy_config in strategies.items():
                    print(f"  {name}:")
                    print(f"    包含模式: {strategy_config.get('include_patterns', [])}")
                    print(f"    清理缓存: {strategy_config.get('clear_cache', False)}")
                    print(f"    重启守护进程: {strategy_config.get('restart_daemon', False)}")
                    print()
                return 0
        except Exception as e:
            print(f"无法读取配置文件: {e}")
            return 1

    # 保存环境变量
    saved_domain_id = os.environ.get('ROS_DOMAIN_ID')
    saved_rmw = os.environ.get('RMW_IMPLEMENTATION')

    try:
        # 初始化ROS2
        rclpy.init()

        # 创建清理器
        cleaner = SmartEnvCleaner(config_file=args.config, strategy=args.strategy)

        if args.dry_run:
            # 干运行模式：只显示将要清理的内容
            print("=== 干运行模式 - 将要清理的内容 ===")
            nodes = cleaner.discover_ros_nodes()
            topics = cleaner.discover_active_topics()
            processes = cleaner.find_ros_processes()

            print(f"将清理 {len(nodes)} 个ROS节点:")
            for node in nodes:
                print(f"  - {node}")

            print(f"\n将清理 {len(topics)} 个用户话题:")
            for topic in topics:
                print(f"  - {topic}")

            print(f"\n将终止 {len(processes)} 个进程:")
            for pid, desc in processes.items():
                print(f"  - {pid}: {desc}")

            success = True
        else:
            # 执行清理
            success = cleaner.run_cleanup()

        # 清理ROS2
        cleaner.destroy_node()
        rclpy.shutdown()

        # 恢复环境变量
        env_config = cleaner.config.get('environment_variables', {})
        defaults = env_config.get('defaults', {})

        if saved_domain_id:
            os.environ['ROS_DOMAIN_ID'] = saved_domain_id
            print(f"恢复 ROS_DOMAIN_ID={saved_domain_id}")
        else:
            default_domain = defaults.get('ROS_DOMAIN_ID', '11')
            os.environ['ROS_DOMAIN_ID'] = default_domain
            print(f"设置默认 ROS_DOMAIN_ID={default_domain}")

        if saved_rmw:
            os.environ['RMW_IMPLEMENTATION'] = saved_rmw
            print(f"恢复 RMW_IMPLEMENTATION={saved_rmw}")
        else:
            default_rmw = defaults.get('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp')
            os.environ['RMW_IMPLEMENTATION'] = default_rmw
            print(f"设置推荐的 RMW_IMPLEMENTATION={default_rmw}")

        return 0 if success else 1

    except Exception as e:
        print(f"清理过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
