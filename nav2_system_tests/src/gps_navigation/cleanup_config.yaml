# Navigation2 环境清理配置文件
# 定义不同场景下的清理策略和进程模式

# 系统基础话题 - 这些话题不应被清理
system_topics:
  - "/parameter_events"
  - "/rosout"

# 系统基础节点 - 这些节点不应被清理
system_nodes:
  - "/ros2cli_daemon"

# ROS进程清理模式
process_patterns:
  # 启动和管理进程
  launch_processes:
    - "ros2.*launch"
    - "ros2.*run"
    - "event_driven_launch.py"
    - "nav2_system_tests"
    - "navigation_launch.py"
    - "bringup_launch.py"
    - "nav2_bringup"
    
  # 仿真相关进程
  simulation_processes:
    - "gazebo"
    - "gz.*sim"
    - "gzserver"
    - "gzclient"
    - "ros_gz_bridge"
    - "parameter_bridge"
    - "spawn_tb3.*"
    - "turtlebot3.*"
    
  # 可视化工具
  visualization_processes:
    - "rviz2"
    - "rqt.*"
    - "foxglove_bridge"
    
  # Nav2核心组件
  nav2_core_processes:
    - "controller_server"
    - "smoother_server"
    - "planner_server"
    - "behavior_server"
    - "velocity_smoother"
    - "collision_monitor"
    - "bt_navigator"
    - "waypoint_follower"
    
  # 定位和地图相关
  localization_processes:
    - "amcl"
    - "map_server"
    - "ekf_node"
    - "navsat_transform_node"
    - "robot_localization.*"
    
  # 基础ROS组件
  basic_ros_processes:
    - "robot_state_publisher"
    - "static_transform_publisher"
    - "lifecycle_manager"
    - "component_container.*"
    
  # GPS导航测试特定进程
  gps_test_processes:
    - "robot_localization_lifecycle_wrapper.py"
    - "indoor_ekf_lifecycle_wrapper.py"
    - "sensor_monitor.py"
    - "tf_monitor.py"
    - "nav_monitor.py"
    - "environment_detector_node.py"
    - "map_manager_node.py"
    - "amcl_auto_initializer.py"

# 缓存清理路径
cache_paths:
  ros_logs:
    - "~/.ros/log/*"
    - "/tmp/.ros*"
    
  nav2_cache:
    - "/tmp/nav2_*"
    - "/tmp/costmap_*"
    - "/tmp/amcl_*"
    - "/tmp/tf_*"
    
  gazebo_cache:
    - "~/.gazebo/log/*"
    - "~/.gz/log/*"
    - "/tmp/gazebo*"
    - "/tmp/gz*"

# 清理策略定义
cleanup_strategies:
  # 轻量级清理 - 只清理用户进程，保留系统进程
  light:
    include_patterns:
      - "nav2_core_processes"
      - "gps_test_processes"
    exclude_patterns: []
    clear_cache: false
    restart_daemon: false
    
  # 标准清理 - 清理大部分ROS进程
  standard:
    include_patterns:
      - "launch_processes"
      - "nav2_core_processes"
      - "localization_processes"
      - "gps_test_processes"
      - "basic_ros_processes"
    exclude_patterns: []
    clear_cache: true
    restart_daemon: true
    
  # 完全清理 - 清理所有ROS相关进程
  full:
    include_patterns:
      - "launch_processes"
      - "simulation_processes"
      - "visualization_processes"
      - "nav2_core_processes"
      - "localization_processes"
      - "basic_ros_processes"
      - "gps_test_processes"
    exclude_patterns: []
    clear_cache: true
    restart_daemon: true
    
  # 仿真专用清理 - 主要清理仿真相关进程
  simulation_only:
    include_patterns:
      - "simulation_processes"
      - "visualization_processes"
    exclude_patterns: []
    clear_cache: false
    restart_daemon: false

# 环境变量管理
environment_variables:
  # 需要保存和恢复的环境变量
  preserve:
    - "ROS_DOMAIN_ID"
    - "RMW_IMPLEMENTATION"
    
  # 需要临时清除的环境变量
  clear_temporarily:
    - "CYCLONEDX_URI"
    - "FASTRTPS_DEFAULT_PROFILES_FILE"
    
  # 默认值
  defaults:
    ROS_DOMAIN_ID: "11"
    RMW_IMPLEMENTATION: "rmw_cyclonedds_cpp"
    CYCLONEDX_URI: "<Discovery><Peers><Peer address=\"localhost\"/></Peers></Discovery>"

# 清理超时设置
timeouts:
  graceful_shutdown: 3  # 优雅关闭等待时间（秒）
  force_kill_wait: 1    # 强制杀死后等待时间（秒）
  daemon_restart: 2     # 守护进程重启等待时间（秒）
  ros_api_timeout: 10   # ROS API调用超时时间（秒）

# 日志设置
logging:
  level: "INFO"  # DEBUG, INFO, WARN, ERROR
  show_process_details: true
  show_topic_details: true
  show_node_details: true
