#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
智能ROS环境清理器 - 简洁版

基于动态发现的完全清理，无需配置文件，一个文件搞定所有清理需求。
"""

import os
import sys
import time
import signal
import psutil
import subprocess
import argparse
from typing import List, Dict
import rclpy
from rclpy.node import Node


class IntelligentCleaner(Node):
    """智能环境清理器 - 简洁版"""

    def __init__(self):
        super().__init__('intelligent_cleaner')

        # 系统基础话题，不应被清理
        self.system_topics = {'/parameter_events', '/rosout'}

        # ROS相关进程关键词（基于经验的核心模式）
        self.ros_keywords = {
            'gazebo', 'gz', 'rviz2', 'rqt', 'foxglove',
            'nav2', 'controller_server', 'planner_server', 'behavior_server',
            'bt_navigator', 'lifecycle_manager', 'robot_state_publisher',
            'static_transform_publisher', 'component_container', 'map_server',
            'amcl', 'ekf_node', 'navsat_transform', 'robot_localization',
            'turtlebot3', 'parameter_bridge', 'spawn_tb3', 'launch'
        }

        # 需要排除的进程模式（系统进程，不应被清理）
        self.exclude_patterns = {
            'ros2cli_daemon',  # ROS2守护进程
            'intelligent_cleaner'  # 当前清理器
        }

        self.get_logger().info("智能环境清理器已启动")

    def discover_ros_nodes(self) -> List[str]:
        """动态发现当前运行的ROS节点"""
        try:
            result = subprocess.run(['ros2', 'node', 'list'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                nodes = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                # 过滤掉自己
                nodes = [n for n in nodes if 'intelligent_cleaner' not in n]
                self.get_logger().info(f"发现 {len(nodes)} 个ROS节点")
                return nodes
            return []
        except Exception as e:
            self.get_logger().warning(f"获取ROS节点列表失败: {e}")
            return []

    def discover_active_topics(self) -> List[str]:
        """动态发现当前活跃的话题"""
        try:
            result = subprocess.run(['ros2', 'topic', 'list'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                topics = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                # 过滤掉系统基础话题
                user_topics = [t for t in topics if t not in self.system_topics]
                self.get_logger().info(f"发现 {len(user_topics)} 个用户话题")
                return user_topics
            return []
        except Exception as e:
            self.get_logger().warning(f"获取话题列表失败: {e}")
            return []

    def discover_ros_processes(self) -> Dict[int, str]:
        """动态发现所有ROS相关进程"""
        ros_processes = {}
        current_pid = os.getpid()

        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                pid = proc.info['pid']
                name = proc.info['name']
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 跳过当前脚本进程
                if pid == current_pid:
                    continue

                # 检查是否为ROS相关进程
                full_command = f"{name} {cmdline}".lower()

                # 先检查是否应该排除
                if any(pattern in full_command for pattern in self.exclude_patterns):
                    continue

                # 再检查是否匹配ROS关键词
                if any(keyword in full_command for keyword in self.ros_keywords):
                    ros_processes[pid] = f"{name} ({cmdline[:80]}...)"

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        self.get_logger().info(f"发现 {len(ros_processes)} 个ROS相关进程")
        return ros_processes

    def stop_ros_daemon(self):
        """停止ROS2守护进程"""
        try:
            self.get_logger().info("停止ROS2守护进程...")
            subprocess.run(['ros2', 'daemon', 'stop'],
                          capture_output=True, timeout=10)
            time.sleep(1)
        except Exception as e:
            self.get_logger().warning(f"停止ROS2守护进程失败: {e}")

    def terminate_processes(self, processes: Dict[int, str], force: bool = False):
        """终止进程"""
        signal_type = signal.SIGKILL if force else signal.SIGTERM
        signal_name = "SIGKILL" if force else "SIGTERM"

        for pid, description in processes.items():
            try:
                if psutil.pid_exists(pid):
                    proc = psutil.Process(pid)
                    self.get_logger().info(f"发送 {signal_name} 到进程 {pid}: {description}")
                    proc.send_signal(signal_type)
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                self.get_logger().warning(f"无法终止进程 {pid}: {e}")

    def clean_cache_files(self):
        """清理缓存文件"""
        self.get_logger().info("清理缓存文件...")

        cache_commands = [
            "rm -rf ~/.ros/log/* 2>/dev/null || true",
            "rm -rf /tmp/.ros* 2>/dev/null || true",
            "rm -rf ~/.gazebo/log/* 2>/dev/null || true",
            "rm -rf ~/.gz/log/* 2>/dev/null || true",
            "find /tmp -name 'nav2_*' -type f -delete 2>/dev/null || true",
            "find /tmp -name 'costmap_*' -type f -delete 2>/dev/null || true",
            "find /tmp -name 'amcl_*' -type f -delete 2>/dev/null || true",
            "find /tmp -name 'tf_*' -type f -delete 2>/dev/null || true",
            "find /tmp -name 'gazebo*' -type f -delete 2>/dev/null || true",
            "find /tmp -name 'gz*' -type f -delete 2>/dev/null || true"
        ]

        for cmd in cache_commands:
            try:
                subprocess.run(cmd, shell=True, capture_output=True, timeout=5)
            except Exception as e:
                self.get_logger().warning(f"清理命令失败 {cmd}: {e}")

    def restart_ros_daemon(self):
        """重启ROS2守护进程"""
        try:
            self.get_logger().info("重启ROS2守护进程...")
            subprocess.run(['ros2', 'daemon', 'start'],
                          capture_output=True, timeout=10)
            time.sleep(2)
        except Exception as e:
            self.get_logger().warning(f"重启ROS2守护进程失败: {e}")

    def verify_cleanup(self) -> bool:
        """验证清理结果"""
        self.get_logger().info("验证清理结果...")

        # 检查剩余话题和进程
        remaining_topics = self.discover_active_topics()
        remaining_processes = self.discover_ros_processes()

        self.get_logger().info(f"剩余用户话题: {len(remaining_topics)}")
        for topic in remaining_topics:
            self.get_logger().info(f"  - {topic}")

        self.get_logger().info(f"剩余ROS进程: {len(remaining_processes)}")
        for pid, desc in remaining_processes.items():
            self.get_logger().info(f"  - {pid}: {desc}")

        # 清理成功的标准：无用户话题，无ROS进程
        success = len(remaining_topics) == 0 and len(remaining_processes) == 0

        if success:
            self.get_logger().info("✅ 环境完全清理成功！")
        else:
            self.get_logger().warning("⚠️  仍有一些资源未完全清理")

        return success

    def run_complete_cleanup(self) -> bool:
        """执行完全清理流程"""
        self.get_logger().info("开始完全清理ROS环境...")

        # 1. 发现当前状态
        nodes = self.discover_ros_nodes()
        topics = self.discover_active_topics()
        processes = self.discover_ros_processes()

        if not nodes and not topics and not processes:
            self.get_logger().info("环境已经是干净的，无需清理")
            return True

        # 2. 停止ROS守护进程
        self.stop_ros_daemon()

        # 3. 优雅地终止进程
        if processes:
            self.get_logger().info("第一轮：优雅终止ROS进程...")
            self.terminate_processes(processes, force=False)
            time.sleep(3)

        # 4. 强制终止剩余进程
        remaining_processes = self.discover_ros_processes()
        if remaining_processes:
            self.get_logger().info("第二轮：强制终止剩余进程...")
            self.terminate_processes(remaining_processes, force=True)
            time.sleep(1)

        # 5. 清理缓存文件
        self.clean_cache_files()

        # 6. 重启ROS守护进程
        self.restart_ros_daemon()

        # 7. 验证清理结果
        return self.verify_cleanup()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能ROS环境完全清理器')
    parser.add_argument('--dry-run', action='store_true',
                       help='只显示将要清理的内容，不实际执行')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='静默模式，减少输出')

    args = parser.parse_args()

    # 保存环境变量
    saved_domain_id = os.environ.get('ROS_DOMAIN_ID')
    saved_rmw = os.environ.get('RMW_IMPLEMENTATION')

    success = False
    cleaner = None

    try:
        # 初始化ROS2
        rclpy.init()

        # 创建清理器
        cleaner = IntelligentCleaner()

        if args.dry_run:
            # 干运行模式：只显示将要清理的内容
            print("=== 干运行模式 - 将要清理的内容 ===")
            nodes = cleaner.discover_ros_nodes()
            topics = cleaner.discover_active_topics()
            processes = cleaner.discover_ros_processes()

            print(f"将清理 {len(nodes)} 个ROS节点:")
            for node in nodes:
                print(f"  - {node}")

            print(f"\n将清理 {len(topics)} 个用户话题:")
            for topic in topics:
                print(f"  - {topic}")

            print(f"\n将终止 {len(processes)} 个进程:")
            for pid, desc in processes.items():
                print(f"  - {pid}: {desc}")

            success = True
        else:
            # 执行完全清理
            success = cleaner.run_complete_cleanup()

    except Exception as e:
        print(f"清理过程中发生错误: {e}")
        success = False

    finally:
        # 安全清理ROS2资源
        try:
            if cleaner is not None:
                cleaner.destroy_node()
            if rclpy.ok():
                rclpy.shutdown()
        except Exception as e:
            print(f"清理ROS2资源时发生错误: {e}")

        # 恢复环境变量
        try:
            if saved_domain_id:
                os.environ['ROS_DOMAIN_ID'] = saved_domain_id
                if not args.quiet:
                    print(f"恢复 ROS_DOMAIN_ID={saved_domain_id}")
            else:
                os.environ['ROS_DOMAIN_ID'] = '11'
                if not args.quiet:
                    print("设置默认 ROS_DOMAIN_ID=11")

            if saved_rmw:
                os.environ['RMW_IMPLEMENTATION'] = saved_rmw
                if not args.quiet:
                    print(f"恢复 RMW_IMPLEMENTATION={saved_rmw}")
            else:
                os.environ['RMW_IMPLEMENTATION'] = 'rmw_cyclonedds_cpp'
                if not args.quiet:
                    print("设置推荐的 RMW_IMPLEMENTATION=rmw_cyclonedds_cpp")

            if not args.quiet:
                print("🎉 智能清理完成！")
        except Exception as e:
            print(f"恢复环境变量时发生错误: {e}")

        return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
